use ratatui::{
    style::{Color, Style},
    text::{Line, Span},
};

pub struct TerminalRenderer {
    // Configuration for rendering
    pub use_colors: bool,
    pub use_unicode: bool,
}

impl TerminalRenderer {
    pub fn new() -> Self {
        Self {
            use_colors: true,
            use_unicode: true,
        }
    }

    pub fn render_to_text(&self, fractal_data: &[Vec<u32>], target_width: usize, target_height: usize) -> Vec<Line> {
        if fractal_data.is_empty() {
            return vec![Line::from("No fractal data")];
        }

        let data_height = fractal_data.len();
        let data_width = fractal_data[0].len();

        // Scale the fractal data to fit the target dimensions
        let mut lines = Vec::new();
        
        for y in 0..target_height.min(data_height) {
            let mut spans = Vec::new();
            
            for x in 0..target_width.min(data_width) {
                let iterations = fractal_data[y][x];
                let (character, color) = self.iterations_to_char_and_color(iterations);
                
                let span = if self.use_colors {
                    Span::styled(character.to_string(), Style::default().fg(color))
                } else {
                    Span::raw(character.to_string())
                };
                
                spans.push(span);
            }
            
            lines.push(Line::from(spans));
        }

        lines
    }

    fn iterations_to_char_and_color(&self, iterations: u32) -> (char, Color) {
        if self.use_unicode {
            self.iterations_to_unicode_char_and_color(iterations)
        } else {
            self.iterations_to_ascii_char_and_color(iterations)
        }
    }

    fn iterations_to_unicode_char_and_color(&self, iterations: u32) -> (char, Color) {
        // Map iterations to Unicode block characters and colors for aesthetic display
        match iterations {
            0..=5 => ('█', Color::Black),
            6..=10 => ('▉', Color::DarkGray),
            11..=15 => ('▊', Color::Gray),
            16..=20 => ('▋', Color::White),
            21..=25 => ('▌', Color::Blue),
            26..=30 => ('▍', Color::Cyan),
            31..=35 => ('▎', Color::Green),
            36..=40 => ('▏', Color::Yellow),
            41..=50 => ('▁', Color::Red),
            51..=60 => ('▂', Color::Magenta),
            61..=70 => ('▃', Color::LightBlue),
            71..=80 => ('▄', Color::LightCyan),
            81..=90 => ('▅', Color::LightGreen),
            91..=100 => ('▆', Color::LightYellow),
            101..=150 => ('▇', Color::LightRed),
            151..=200 => ('█', Color::LightMagenta),
            _ => ('░', Color::White),
        }
    }

    fn iterations_to_ascii_char_and_color(&self, iterations: u32) -> (char, Color) {
        // Map iterations to ASCII characters and colors
        match iterations {
            0..=5 => (' ', Color::Black),
            6..=10 => ('.', Color::DarkGray),
            11..=15 => (':', Color::Gray),
            16..=20 => (';', Color::White),
            21..=25 => ('!', Color::Blue),
            26..=30 => ('|', Color::Cyan),
            31..=35 => ('$', Color::Green),
            36..=40 => ('@', Color::Yellow),
            41..=50 => ('&', Color::Red),
            51..=60 => ('%', Color::Magenta),
            61..=70 => ('*', Color::LightBlue),
            71..=80 => ('+', Color::LightCyan),
            81..=90 => ('=', Color::LightGreen),
            91..=100 => ('-', Color::LightYellow),
            101..=150 => ('~', Color::LightRed),
            151..=200 => ('#', Color::LightMagenta),
            _ => ('?', Color::White),
        }
    }

    pub fn set_use_colors(&mut self, use_colors: bool) {
        self.use_colors = use_colors;
    }

    pub fn set_use_unicode(&mut self, use_unicode: bool) {
        self.use_unicode = use_unicode;
    }

    // Method to render fractal data to a simple string (for debugging or text output)
    pub fn render_to_string(&self, fractal_data: &[Vec<u32>]) -> String {
        let mut result = String::new();
        
        for row in fractal_data {
            for &iterations in row {
                let (character, _) = self.iterations_to_char_and_color(iterations);
                result.push(character);
            }
            result.push('\n');
        }
        
        result
    }

    // Method to get color palette information
    pub fn get_color_info(&self) -> Vec<(String, Color)> {
        vec![
            ("Very Low (0-5)".to_string(), Color::Black),
            ("Low (6-15)".to_string(), Color::DarkGray),
            ("Medium-Low (16-25)".to_string(), Color::Blue),
            ("Medium (26-40)".to_string(), Color::Green),
            ("Medium-High (41-70)".to_string(), Color::Yellow),
            ("High (71-100)".to_string(), Color::Red),
            ("Very High (101-200)".to_string(), Color::Magenta),
            ("Extreme (200+)".to_string(), Color::White),
        ]
    }
}

impl Default for TerminalRenderer {
    fn default() -> Self {
        Self::new()
    }
}
