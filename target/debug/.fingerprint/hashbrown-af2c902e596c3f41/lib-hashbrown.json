{"rustc": 15111467291198882907, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 5347358027863023418, "path": 8212096197971754594, "deps": [[5230392855116717286, "equivalent", false, 3451099848162169302], [9150530836556604396, "allocator_api2", false, 12298644208464329900], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 11807892534316460068]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-af2c902e596c3f41/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}