{"rustc": 15111467291198882907, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 3033921117576893, "path": 13714440359597815953, "deps": [[1988483478007900009, "unicode_ident", false, 2481714648974038193], [3060637413840920116, "proc_macro2", false, 9016942406719771295], [17990358020177143287, "quote", false, 7024335311951718139]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-56662e99db294349/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}