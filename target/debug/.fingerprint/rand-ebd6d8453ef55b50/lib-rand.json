{"rustc": 15111467291198882907, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 8276155916380437441, "path": 3179986570967055202, "deps": [[1573238666360410412, "rand_chacha", false, 6708789979603743803], [5330658427305787935, "libc", false, 17305623641329000967], [18130209639506977569, "rand_core", false, 8066578588044661809]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-ebd6d8453ef55b50/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}