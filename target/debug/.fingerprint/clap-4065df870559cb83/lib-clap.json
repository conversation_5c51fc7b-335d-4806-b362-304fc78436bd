{"rustc": 15111467291198882907, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 9589772125425470163, "path": 17697257972847224478, "deps": [[4925398738524877221, "clap_derive", false, 8188038420027169549], [14814905555676593471, "clap_builder", false, 1166875691812500018]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-4065df870559cb83/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}