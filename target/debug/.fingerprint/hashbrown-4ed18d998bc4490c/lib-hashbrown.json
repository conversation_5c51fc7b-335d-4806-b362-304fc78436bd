{"rustc": 15111467291198882907, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 8276155916380437441, "path": 8212096197971754594, "deps": [[5230392855116717286, "equivalent", false, 18262357679944519570], [9150530836556604396, "allocator_api2", false, 9497616155842723229], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 14929728785357624771]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-4ed18d998bc4490c/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}